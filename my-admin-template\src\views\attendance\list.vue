<template>
  <div class="attendance-list-container">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <h2>
            <el-icon><Calendar /></el-icon>
            考勤列表
          </h2>
          <div class="header-actions">
            <el-button type="primary" :icon="Plus" @click="handleAddAttendance">
              添加考勤记录
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 日历组件 -->
      <div class="calendar-container" v-loading="loading">
        <el-calendar v-model="selectedDate">
          <template #date-cell="{ data }">
            <div class="calendar-cell" :class="getCellClass(data.day)">
              <!-- 日期数字 - 左上角 -->
              <div class="date-number">{{ data.day.split('-').slice(-1)[0] }}</div>

              <!-- 信息区域 - 底部 -->
              <div class="info-container">
                <!-- 考勤信息 -->
                <div class="attendance-info" v-if="getAttendanceInfo(data.day) || shouldShowAbsent(data.day)">
                  <el-tag
                    :type="getDisplayInfo(data.day).type"
                    size="small"
                    class="attendance-tag"
                  >
                    {{ getAttendanceDisplayText(getDisplayInfo(data.day)) }}
                  </el-tag>
                  <div class="time-info" v-if="getDisplayInfo(data.day).time !== '--'">
                    {{ getTimeDisplayText(getDisplayInfo(data.day).time) }}
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-calendar>
      </div>
      
      <!-- 统计信息 -->
      <el-divider />
      <div class="statistics-section">
        <h3>本月考勤统计</h3>
        <el-row :gutter="20">
          <el-col :span="4">
            <el-statistic title="出勤天数" :value="statistics.present" suffix="天">
              <template #prefix>
                <el-icon style="color: #67c23a;"><Check /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic title="迟到次数" :value="statistics.late" suffix="次">
              <template #prefix>
                <el-icon style="color: #e6a23c;"><Warning /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic title="早退次数" :value="statistics.earlyLeave" suffix="次">
              <template #prefix>
                <el-icon style="color: #f56c6c;"><Close /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic title="缺勤天数" :value="statistics.absent" suffix="天">
              <template #prefix>
                <el-icon style="color: #909399;"><Remove /></el-icon>
              </template>
            </el-statistic>
          </el-col>
          <el-col :span="4">
            <el-statistic title="加班天数" :value="statistics.overtime" suffix="天">
              <template #prefix>
                <el-icon style="color: #e6a23c;"><Plus /></el-icon>
              </template>
            </el-statistic>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 添加考勤记录对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      title="添加考勤记录" 
      width="500px"
      @close="resetForm"
    >
      <el-form :model="attendanceForm" label-width="100px">
        <el-form-item label="日期">
          <el-date-picker
            v-model="attendanceForm.date"
            type="date"
            placeholder="选择日期"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="上班时间">
          <el-time-picker
            v-model="attendanceForm.checkIn"
            placeholder="选择上班时间"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="下班时间">
          <el-time-picker
            v-model="attendanceForm.checkOut"
            placeholder="选择下班时间"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="attendanceForm.status" placeholder="选择状态" style="width: 100%;">
            <el-option label="正常" value="normal" />
            <el-option label="迟到" value="late" />
            <el-option label="早退" value="early" />
            <el-option label="缺勤" value="absent" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="attendanceForm.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveAttendance">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { Calendar, Plus, Check, Warning, Close, Remove } from '@element-plus/icons-vue'
import { getWechatAttendanceApi, getGeneralAttendanceApi } from '@/api/user'
import holidaysData from '@/data/holidays.json'

// 响应式数据
const store = useStore()
const selectedDate = ref(new Date())
const dialogVisible = ref(false)
const loading = ref(false)

// 考勤表单数据
const attendanceForm = reactive({
  date: '',
  checkIn: '',
  checkOut: '',
  status: 'normal',
  remark: ''
})

// 考勤数据
const attendanceData = ref({})

// 假期数据处理
const holidayMap = ref({})

// 初始化假期数据
const initHolidayData = () => {
  const map = {}
  holidaysData.holidays.forEach(holiday => {
    map[holiday.date] = holiday
  })
  holidayMap.value = map
}

// 检查是否为假期
const isHoliday = (date) => {
  return holidayMap.value[date] || null
}

// 判断是否为工作日（非假期且不是未来日期）
const isWorkday = (date) => {
  const targetDate = new Date(date)
  const today = new Date()
  today.setHours(23, 59, 59, 999) // 设置为今天的最后一刻

  // 如果是未来日期，不算工作日
  if (targetDate > today) {
    return false
  }

  // 如果是假期，不算工作日
  if (isHoliday(date)) {
    return false
  }

  return true
}

// 判断是否应该显示缺勤状态
const shouldShowAbsent = (date) => {
  // 如果已经有考勤数据，不显示缺勤
  if (getAttendanceInfo(date)) {
    return false
  }

  // 如果是工作日且没有考勤数据，显示缺勤
  return isWorkday(date)
}

// 获取显示信息（考勤数据或缺勤状态）
const getDisplayInfo = (date) => {
  const attendanceInfo = getAttendanceInfo(date)
  const holiday = isHoliday(date)

  // 如果有考勤数据
  if (attendanceInfo) {
    // 如果是假期且有考勤数据，显示为加班
    if (holiday) {
      return {
        ...attendanceInfo,
        status: '加班',
        type: 'warning' // 使用警告类型的颜色
      }
    }
    // 否则返回原始考勤数据
    return attendanceInfo
  }

  // 如果应该显示缺勤，返回缺勤信息
  if (shouldShowAbsent(date)) {
    return {
      status: '缺勤',
      type: 'danger',
      time: '--'
    }
  }

  // 其他情况返回空
  return null
}

// 获取日历当前显示月份的日期范围
const getCalendarDateRange = (date) => {
  const currentDate = new Date(date)
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()

  // 获取当月第一天
  const firstDay = new Date(year, month, 1)
  // 获取当月最后一天
  const lastDay = new Date(year, month + 1, 0)

  // 获取日历显示的第一天（可能是上个月的日期）
  const calendarStart = new Date(firstDay)
  calendarStart.setDate(firstDay.getDate() - firstDay.getDay()) // 周日为0

  // 获取日历显示的最后一天（可能是下个月的日期）
  const calendarEnd = new Date(lastDay)
  calendarEnd.setDate(lastDay.getDate() + (6 - lastDay.getDay()))

  return {
    calendarStart,
    calendarEnd,
    monthStart: firstDay,
    monthEnd: lastDay
  }
}

// 格式化日期为 yyyy-MM-dd
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}



// 解析时间字符串，支持两种格式
const parseTimeString = (timeStr) => {
  if (!timeStr) return null

  // 支持两种格式：
  // 1. "2025-06-06 08:30:00" (空格分隔)
  // 2. "2025-06-06T08:30:00" (ISO格式)
  let datePart, timePart

  if (timeStr.includes('T')) {
    // ISO格式
    [datePart, timePart] = timeStr.split('T')
  } else if (timeStr.includes(' ')) {
    // 空格分隔格式
    [datePart, timePart] = timeStr.split(' ')
  } else {
    console.warn('无法解析时间格式:', timeStr)
    return null
  }

  return {
    date: datePart,
    time: timePart ? timePart.substring(0, 5) : null // 只取 HH:MM 部分
  }
}

// 处理API返回的数据
const processAttendanceData = (data) => {
  const processedData = {}
  data.forEach((item) => {
    if (item.punchList && item.punchList.length > 0) {
      // 获取第一个打卡时间的日期
      const firstPunch = parseTimeString(item.punchList[0].time)
      if (!firstPunch) {
        console.warn('跳过无效的打卡记录:', item.punchList[0])
        return
      }

      const date = firstPunch.date

      // 构建时间范围字符串
      let timeRange = '--'
      if (item.punchList.length >= 2) {
        const startPunch = parseTimeString(item.punchList[0].time)
        const endPunch = parseTimeString(item.punchList[1].time)

        if (startPunch && endPunch && startPunch.time && endPunch.time) {
          timeRange = `${startPunch.time}-${endPunch.time}`
        }
      } else if (item.punchList.length === 1) {
        const startPunch = parseTimeString(item.punchList[0].time)
        if (startPunch && startPunch.time) {
          timeRange = `${startPunch.time}-未打卡`
        }
      }

      processedData[date] = {
        status: '正常', // 统一设置为正常
        type: 'success', // 统一设置为成功类型
        time: timeRange,
        originalData: item // 保存原始数据以备后用
      }
    }
  })
  return processedData
}

// 获取考勤数据
const fetchAttendanceData = async (targetDate = selectedDate.value) => {
  return new Promise(async (resolve, reject) => {
    try {
    loading.value = true
    const userID = store.getters.userID
    const token = store.getters.token

    if (!userID) {
      ElMessage.error('请先登录')
      return
    }

    const dateRange = getCalendarDateRange(targetDate)
    const { calendarStart, calendarEnd } = dateRange

    // 判断接口调用逻辑
    const today = new Date()
    const currentMonth = today.getMonth() // 0-11
    const currentYear = today.getFullYear()
    const calendarMonth = calendarStart.getMonth() // 0-11
    const calendarYear = calendarStart.getFullYear()

    // 计算月份差值
    const monthDiff = (currentYear - calendarYear) * 12 + (currentMonth - calendarMonth)

    // 如果日历显示的是当前月份或者前1个月内，使用近期数据接口
    const needRecentData = monthDiff <= 1 // 当前月份或上个月
    const needHistoricalData = monthDiff > 1 // 超过1个月的历史数据

    let allData = []

    // 获取近1个月数据
    if (needRecentData) {
      try {
        const recentResponse = await getWechatAttendanceApi(userID)
        if (recentResponse.data) {
          allData = allData.concat(recentResponse.data)
        }
      } catch (error) {
        console.error('获取近期考勤数据失败:', error)
        ElMessage.warning('获取近期考勤数据失败')
      }
    }

    // 获取历史数据
    if (needHistoricalData && token) {
      try {
        const historicalResponse = await getGeneralAttendanceApi({
          cookie: token,
          userID: userID,
          startDate: formatDate(calendarStart),
          endDate: formatDate(calendarEnd)
        })
        // 历史数据接口返回的数据结构：{ data: { success: true, data: [...] } }
        if (historicalResponse.data && historicalResponse.data.data && historicalResponse.data.success) {
          const historicalData = historicalResponse.data.data
          allData = allData.concat(historicalData)
        } else {
          console.warn('历史数据格式异常:', historicalResponse.data)
        }
      } catch (error) {
        console.error('获取历史考勤数据失败:', error)
        if (error.response && error.response.status === 401) {
          ElMessage.error('登录已过期，请重新登录')
          // 可以在这里触发重新登录逻辑
          store.dispatch('user/resetToken')
        } else {
          ElMessage.warning('获取历史考勤数据失败')
        }
      }
    }

    // 处理并合并数据
    const processedData = processAttendanceData(allData)
    attendanceData.value = processedData

      // 更新假期和缺勤td样式
      updateCellTdStyles()
      // 重新添加事件监听器
      addCalendarEventListeners()

      resolve()

    } catch (error) {
      console.error('获取考勤数据失败:', error)
      ElMessage.error('获取考勤数据失败')
      reject(error)
    } finally {
      loading.value = false
    }
  })
}

// 统计数据
const statistics = computed(() => {
  const stats = {
    present: 0,
    late: 0,
    earlyLeave: 0,
    absent: 0,
    overtime: 0 // 新增加班统计
  }

  // 获取当前日历显示月份的所有日期
  const currentDate = selectedDate.value
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth()
  const daysInMonth = new Date(year, month + 1, 0).getDate()

  // 遍历当月每一天
  for (let day = 1; day <= daysInMonth; day++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
    const displayInfo = getDisplayInfo(dateStr)

    if (displayInfo) {
      switch (displayInfo.status) {
        case '正常':
          stats.present++
          break
        case '迟到':
          stats.late++
          break
        case '早退':
          stats.earlyLeave++
          break
        case '缺勤':
          stats.absent++
          break
        case '加班':
          stats.overtime++
          break
      }
    }
  }

  return stats
})

// 方法
const getAttendanceInfo = (date) => {
  return attendanceData.value[date] || null
}



// 获取日历单元格样式类
const getCellClass = (date) => {
  const holiday = isHoliday(date)
  const absent = shouldShowAbsent(date)

  let classes = []

  if (holiday) {
    classes.push('holiday-cell')
  }

  if (absent) {
    classes.push('absent-cell')
  }

  return classes.join(' ')
}

// 获取考勤状态显示文本
const getAttendanceDisplayText = (displayInfo) => {
  if (!displayInfo) return ''
  return displayInfo.status
}

// 获取时间显示文本
const getTimeDisplayText = (time) => {
  if (!time || time === '--') return ''
  return time
}

// 监听 selectedDate 的变化
watch(selectedDate, (newDate, oldDate) => {
  if (newDate && oldDate && newDate.getTime() !== oldDate.getTime()) {
    // 检查是否是月份变化
    if (newDate.getMonth() !== oldDate.getMonth() || newDate.getFullYear() !== oldDate.getFullYear()) {
      fetchAttendanceData(newDate).then(() => {
        updateCellTdStyles()
        addCalendarEventListeners()
      })
    }
  }
})

// 监听假期数据变化，更新td样式
watch(() => holidayMap.value, () => {
  updateCellTdStyles()
}, { deep: true })

// 监听考勤数据变化，更新td样式
watch(() => attendanceData.value, () => {
  updateCellTdStyles()
}, { deep: true })

const handleAddAttendance = () => {
  dialogVisible.value = true
  attendanceForm.date = new Date()
}

const handleSaveAttendance = () => {
  // 这里添加保存考勤记录的逻辑
  ElMessage.success('考勤记录保存成功！')
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(attendanceForm, {
    date: '',
    checkIn: '',
    checkOut: '',
    status: 'normal',
    remark: ''
  })
}

// 为假期和缺勤单元格的父级td添加样式类
const updateCellTdStyles = () => {
  // 等待DOM更新完成
  setTimeout(() => {
    // 移除所有现有的样式类
    document.querySelectorAll('.holiday-td, .absent-td').forEach(td => {
      td.classList.remove('holiday-td', 'absent-td')
    })

    // 为包含假期单元格的td添加类
    document.querySelectorAll('.holiday-cell').forEach(cell => {
      const td = cell.closest('td')
      if (td) {
        td.classList.add('holiday-td')
      }
    })

    // 为包含缺勤单元格的td添加类
    document.querySelectorAll('.absent-cell').forEach(cell => {
      const td = cell.closest('td')
      if (td) {
        td.classList.add('absent-td')
      }
    })
  }, 100)
}

// 添加日历事件监听器
const addCalendarEventListeners = () => {
  setTimeout(() => {
    const calendarTable = document.querySelector('.el-calendar-table')
    if (calendarTable) {
      // 移除之前的监听器（如果存在）
      calendarTable.removeEventListener('click', handleCalendarClick, true)
      calendarTable.removeEventListener('mouseenter', handleCalendarMouseEnter, true)
      calendarTable.removeEventListener('mouseleave', handleCalendarMouseLeave, true)

      // 添加新的监听器，使用捕获模式确保优先处理
      calendarTable.addEventListener('click', handleCalendarClick, true)
      calendarTable.addEventListener('mouseenter', handleCalendarMouseEnter, true)
      calendarTable.addEventListener('mouseleave', handleCalendarMouseLeave, true)
    }
  }, 200)
}

// 处理日历点击事件
const handleCalendarClick = (event) => {
  const clickedTd = event.target.closest('td')

  // 检查是否点击了非当月的日期（上个月或下个月的日期）
  if (clickedTd) {
    const isCurrentMonth = clickedTd.classList.contains('current')
    const isPrevMonth = clickedTd.classList.contains('prev')
    const isNextMonth = clickedTd.classList.contains('next')

    // 如果点击的是上个月或下个月的日期，阻止默认行为
    if (isPrevMonth || isNextMonth) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }

    // 如果点击的是当月日期，也阻止默认行为（因为我们不希望触发任何接口调用）
    if (isCurrentMonth) {
      event.preventDefault()
      event.stopPropagation()
    }
  }

  // 立即执行一次样式更新
  updateCellTdStyles()
  // 多次延迟执行，确保DOM更新完成后样式正确
  setTimeout(() => {
    updateCellTdStyles()
  }, 10)
  setTimeout(() => {
    updateCellTdStyles()
  }, 50)
  setTimeout(() => {
    updateCellTdStyles()
  }, 100)
}

// 处理鼠标进入事件
const handleCalendarMouseEnter = (event) => {
  const td = event.target.closest('td')
  if (td && (td.classList.contains('holiday-td') || td.classList.contains('absent-td'))) {
    // 延迟执行，确保样式重新应用
    setTimeout(() => {
      updateCellTdStyles()
    }, 10)
  }
}

// 处理鼠标离开事件
const handleCalendarMouseLeave = (event) => {
  const td = event.target.closest('td')
  if (td && (td.classList.contains('holiday-td') || td.classList.contains('absent-td'))) {
    // 延迟执行，确保样式重新应用
    setTimeout(() => {
      updateCellTdStyles()
    }, 10)
  }
}

// 页面加载时获取考勤数据
onMounted(() => {
  // 初始化假期数据
  initHolidayData()
  // 延迟一下确保 store 已经初始化
  setTimeout(() => {
    fetchAttendanceData().then(() => {
      updateCellTdStyles()
      addCalendarEventListeners()
    })
  }, 100)
})
</script>

<style scoped>
.attendance-list-container {
  padding: 24px;
  background-color: var(--el-bg-color-page);
  min-height: 100vh;
  min-width: 1200px; /* 设定最小宽度 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.calendar-container {
  margin: 24px 0;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-cell {
  height: 100%;
  padding: 6px;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.date-number {
  font-weight: bold;
  position: absolute;
  top: 6px;
  left: 6px;
  z-index: 3;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.info-container {
  position: absolute;
  bottom: 6px;
  left: 6px;
  right: 6px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  z-index: 2;
}



.attendance-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  flex: 1;
  justify-content: flex-end;
}



.attendance-tag {
  font-size: 12px;
  padding: 3px 8px;
  white-space: nowrap;
  font-weight: 500;
}

.time-info {
  font-size: 12px;
  color: var(--el-text-color-primary);
  font-weight: 600;
  text-align: right;
  white-space: nowrap;
  margin-top: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 1px 4px;
  border-radius: 3px;
}

.statistics-section {
  margin-top: 24px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statistics-section h3 {
  margin-bottom: 20px;
  color: var(--el-text-color-primary);
  font-size: 20px;
  font-weight: 600;
}

:deep(.el-calendar-table .el-calendar-day) {
  height: 90px;
  min-height: 70px;
}

/* 增强统计数据的显示 */
:deep(.el-statistic__content) {
  font-size: 28px !important;
  font-weight: 700 !important;
}

:deep(.el-statistic__head) {
  font-size: 16px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
}

/* 移除移动端适配，使用固定最小宽度 */

:deep(.el-calendar__header) {
  padding: 12px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

/* 假期单元格样式 - 在td层添加背景色 */
:deep(.el-calendar-table .holiday-td) {
  background-color: #E6A23C !important; /* Element Plus 警告色 - 温和的橙黄色 */
}

/* 假期单元格hover状态 - 保持背景色 */
:deep(.el-calendar-table .holiday-td:hover) {
  background-color: #E6A23C !important; /* 保持相同颜色 */
}

/* 假期单元格内部元素hover状态 */
:deep(.el-calendar-table .holiday-td .el-calendar-day:hover) {
  background-color: transparent !important; /* 透明背景，显示td的背景色 */
}

/* 缺勤单元格样式 - 在td层添加背景色 */
:deep(.el-calendar-table .absent-td) {
  background-color: #F56C6C !important; /* Element Plus 危险色 - 红色背景 */
}

/* 缺勤单元格hover状态 - 保持背景色 */
:deep(.el-calendar-table .absent-td:hover) {
  background-color: #F56C6C !important; /* 保持相同颜色 */
}

/* 缺勤单元格内部元素hover状态 */
:deep(.el-calendar-table .absent-td .el-calendar-day:hover) {
  background-color: transparent !important; /* 透明背景，显示td的背景色 */
}

/* 缺勤单元格的日期数字样式 */
:deep(.absent-td .date-number) {
  color: #fff !important; /* 白色日期数字，在红色背景上更清晰 */
  font-weight: bold;
}

.absent-cell .date-number {
  color: #fff; /* 白色日期数字，在红色背景上更清晰 */
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 对话框样式优化 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-select .el-input__inner) {
  font-size: 14px;
}

:deep(.el-textarea__inner) {
  font-size: 14px;
}

/* 按钮样式优化 */
:deep(.el-button) {
  font-size: 14px;
  font-weight: 500;
  padding: 10px 20px;
}

:deep(.el-button--primary) {
  font-weight: 600;
}

/* 日历头部样式优化 */
:deep(.el-calendar__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-calendar__title) {
  font-size: 18px;
  font-weight: 600;
}

:deep(.el-button-group .el-button) {
  font-size: 14px;
}
</style>
