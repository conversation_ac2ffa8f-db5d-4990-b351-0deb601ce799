<template>
  <div class="theme-test-container">
    <el-card shadow="hover" class="theme-info-card">
      <template #header>
        <div class="card-header">
          <h2>🎨 主题切换测试页面</h2>
        </div>
      </template>
      
      <div class="theme-status">
        <el-alert
          :title="`当前主题: ${currentTheme === 'dark' ? '🌙 暗黑模式' : '☀️ 亮色模式'}`"
          :type="currentTheme === 'dark' ? 'info' : 'success'"
          :icon="currentTheme === 'dark' ? 'Moon' : 'Sunny'"
          show-icon
          :closable="false"
          class="theme-status-alert"
        />
        <div class="theme-transition-demo">
          <p>✨ 完美的涟漪"关灯"效果：</p>
          <ul>
            <li>🎨 Element Plus 官方暗黑模式支持</li>
            <li>🔧 VueUse useDark 自动管理状态</li>
            <li>💫 真正的涟漪扩散动画</li>
            <li>🌙 反向遮罩技术，无白屏/黑屏</li>
            <li>🔄 图标旋转和缩放动画</li>
            <li>🎯 防抖点击保护</li>
            <li>🌊 全局元素同步过渡</li>
            <li>📋 侧边栏主题自适应</li>
            <li>🎪 菜单项悬停和激活状态</li>
            <li>⚡ 完美的视觉体验</li>
          </ul>
          <p style="margin-top: 15px; font-style: italic; color: var(--el-color-primary);">
            💡 点击任何主题切换按钮，体验从点击位置开始的涟漪扩散效果！
          </p>
        </div>
      </div>

      <div class="theme-controls">
        <h3>主题控制</h3>
        <el-space>
          <el-button
            type="primary"
            :icon="isDark ? 'Sunny' : 'Moon'"
            @click="toggleTheme($event)"
          >
            切换到{{ isDark ? '亮色' : '暗黑' }}模式
          </el-button>
          <el-button @click="setLightTheme($event)" :disabled="!isDark">
            设置亮色模式
          </el-button>
          <el-button @click="setDarkTheme($event)" :disabled="isDark">
            设置暗黑模式
          </el-button>
        </el-space>
      </div>

      <div class="component-showcase">
        <h3>组件展示</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="hover">
              <h4>表单组件</h4>
              <el-form label-width="80px">
                <el-form-item label="用户名">
                  <el-input v-model="testForm.username" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item label="密码">
                  <el-input v-model="testForm.password" type="password" placeholder="请输入密码" />
                </el-form-item>
                <el-form-item label="性别">
                  <el-radio-group v-model="testForm.gender">
                    <el-radio value="male">男</el-radio>
                    <el-radio value="female">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="爱好">
                  <el-checkbox-group v-model="testForm.hobbies">
                    <el-checkbox value="reading">阅读</el-checkbox>
                    <el-checkbox value="music">音乐</el-checkbox>
                    <el-checkbox value="sports">运动</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <h4>按钮和标签</h4>
              <el-space direction="vertical" style="width: 100%">
                <el-space>
                  <el-button>默认按钮</el-button>
                  <el-button type="primary">主要按钮</el-button>
                  <el-button type="success">成功按钮</el-button>
                  <el-button type="warning">警告按钮</el-button>
                  <el-button type="danger">危险按钮</el-button>
                </el-space>
                <el-space>
                  <el-tag>默认标签</el-tag>
                  <el-tag type="success">成功标签</el-tag>
                  <el-tag type="info">信息标签</el-tag>
                  <el-tag type="warning">警告标签</el-tag>
                  <el-tag type="danger">危险标签</el-tag>
                </el-space>
              </el-space>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div class="table-showcase">
        <h3>表格展示</h3>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="age" label="年龄" width="80" />
          <el-table-column prop="email" label="邮箱" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '活跃' ? 'success' : 'info'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useDark, useToggle } from '@vueuse/core'

// 主题相关 - 使用 VueUse
const isDark = useDark({
  storageKey: 'admin-theme-mode'
})

const toggleDark = useToggle(isDark)
const currentTheme = computed(() => isDark.value ? 'dark' : 'light')

const toggleTheme = (event) => {
  // 创建真正的涟漪扩散效果
  createRippleTransition(event)
  toggleDark()
}

const setLightTheme = (event) => {
  if (isDark.value) {
    createRippleTransition(event)
    isDark.value = false
  }
}

const setDarkTheme = (event) => {
  if (!isDark.value) {
    createRippleTransition(event)
    isDark.value = true
  }
}

// 创建涟漪扩散效果（无白屏版本）
const createRippleTransition = (event) => {
  // 获取点击位置
  const rect = event.currentTarget.getBoundingClientRect()
  const x = rect.left + rect.width / 2
  const y = rect.top + rect.height / 2

  // 计算需要覆盖整个屏幕的半径
  const maxRadius = Math.sqrt(
    Math.pow(Math.max(x, window.innerWidth - x), 2) +
    Math.pow(Math.max(y, window.innerHeight - y), 2)
  )

  // 创建一个临时的页面快照作为旧主题的覆盖层
  const overlay = document.createElement('div')
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: ${!isDark.value ? '#f0f2f5' : '#111827'};
    pointer-events: none;
    z-index: 9998;
    clip-path: circle(${maxRadius}px at ${x}px ${y}px);
    transition: clip-path 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  `

  document.body.appendChild(overlay)

  // 开始收缩动画，露出下面的新主题
  requestAnimationFrame(() => {
    overlay.style.clipPath = `circle(0px at ${x}px ${y}px)`
  })

  // 动画完成后移除覆盖层
  setTimeout(() => {
    if (overlay.parentNode) {
      overlay.parentNode.removeChild(overlay)
    }
  }, 600)
}

// 测试表单数据
const testForm = ref({
  username: '',
  password: '',
  gender: 'male',
  hobbies: ['reading']
})

// 测试表格数据
const tableData = ref([
  { name: '张三', age: 25, email: '<EMAIL>', status: '活跃' },
  { name: '李四', age: 30, email: '<EMAIL>', status: '离线' },
  { name: '王五', age: 28, email: '<EMAIL>', status: '活跃' },
  { name: '赵六', age: 35, email: '<EMAIL>', status: '离线' }
])
</script>

<style scoped>
.theme-test-container {
  padding: 20px;
}

.theme-info-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header h2 {
  margin: 0;
  color: var(--navbar-text-color, #303133);
}

.theme-status {
  margin-bottom: 30px;
}

.theme-controls {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--el-bg-color-page, #f5f7fa);
  border-radius: 8px;
}

.component-showcase {
  margin-bottom: 30px;
}

.table-showcase {
  margin-bottom: 20px;
}

h3, h4 {
  color: var(--navbar-text-color, #303133);
  margin-bottom: 15px;
}

.theme-status-alert {
  margin-bottom: 20px;
}

.theme-transition-demo {
  background: linear-gradient(135deg, var(--el-color-primary-light-9, #ecf5ff), var(--el-color-success-light-9, #f0f9ff));
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light, #ebeef5);
  margin-top: 15px;
}

.theme-transition-demo p {
  margin: 0 0 10px 0;
  font-weight: 600;
  color: var(--navbar-text-color, #303133);
}

.theme-transition-demo ul {
  margin: 0;
  padding-left: 20px;
}

.theme-transition-demo li {
  margin-bottom: 5px;
  color: var(--el-text-color-regular, #606266);
}
</style>
