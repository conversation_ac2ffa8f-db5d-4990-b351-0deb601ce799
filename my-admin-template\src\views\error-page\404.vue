<template>
  <div class="error-page">
    <el-result icon="error" title="404 - 页面未找到" sub-title="抱歉，您访问的页面不存在。">
      <template #extra>
        <el-button type="primary" @click="goHome">返回首页</el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
const goHome = () => {
  router.push('/')
}
</script>

<style scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - var(--navbar-height) - var(--app-main-padding) * 2); /* 减去Navbar和AppMain的padding */
  min-height: 400px; /* 保证最小高度 */
}
</style>