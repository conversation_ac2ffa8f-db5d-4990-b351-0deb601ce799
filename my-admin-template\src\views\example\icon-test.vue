<template>
  <div class="icon-test-container">
    <el-card shadow="hover">
      <template #header>
        <h2>🎨 图标测试页面</h2>
      </template>
      
      <div class="icon-grid">
        <div class="icon-item" v-for="icon in testIcons" :key="icon.name">
          <el-icon :size="24">
            <component :is="icon.name" />
          </el-icon>
          <span>{{ icon.name }}</span>
        </div>
      </div>
      
      <el-divider />
      
      <div class="menu-icons">
        <h3>菜单中使用的图标：</h3>
        <div class="icon-grid">
          <div class="icon-item" v-for="icon in menuIcons" :key="icon.name">
            <el-icon :size="24">
              <component :is="icon.name" />
            </el-icon>
            <span>{{ icon.name }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 测试常用图标
const testIcons = ref([
  { name: 'House' },
  { name: 'Menu' },
  { name: 'Grid' },
  { name: 'User' },
  { name: 'Setting' },
  { name: 'Document' },
  { name: 'Folder' },
  { name: 'Star' },
  { name: 'Bell' },
  { name: 'Search' }
])

// 菜单中使用的图标
const menuIcons = ref([
  { name: 'House' },
  { name: 'Menu' },
  { name: 'Grid' },
  { name: 'Tickets' },
  { name: 'Brush' }
])
</script>

<style scoped>
.icon-test-container {
  padding: 20px;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.icon-item:hover {
  background-color: var(--el-bg-color-page);
  border-color: var(--el-color-primary);
}

.icon-item span {
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  text-align: center;
}

h2, h3 {
  color: var(--navbar-text-color, #303133);
  margin-bottom: 15px;
}
</style>
