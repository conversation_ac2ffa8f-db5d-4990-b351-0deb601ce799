<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="isMobile && !isSidebarCollapse" class="drawer-bg" @click="handleClickOutside" />
    <Sidebar class="sidebar-container" :is-collapse="isSidebarCollapse" />
    <div class="main-container">
      <div class="fixed-header">
        <Navbar 
            :is-sidebar-collapse-prop="isSidebarCollapse" @toggle-sidebar="toggleSidebar" 
        />
      </div>
      <AppMain />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import Navbar from './components/Navbar.vue'
import Sidebar from './components/Sidebar.vue'
import AppMain from './components/AppMain.vue'

const MOBILE_BREAKPOINT = 992; // 移动端断点

// ref 定义
const isSidebarCollapse = ref(false);     // 当前侧边栏的实际折叠状态 (false = 展开)
const isMobile = ref(false);              // 当前是否为移动端视图
const userDesktopPrefersCollapsed = ref(false); // 用户在【桌面端】是否偏好折叠状态 (false = 偏好展开)

// 根据屏幕宽度更新布局状态的函数
const updateLayoutBasedOnScreenWidth = () => {
  const currentlyMobile = window.innerWidth < MOBILE_BREAKPOINT;

  if (isMobile.value && !currentlyMobile) {
    // 从移动端 ==> 切换到桌面端
    isMobile.value = false;
    isSidebarCollapse.value = userDesktopPrefersCollapsed.value; // 恢复用户在桌面端的偏好设置
  } else if (!isMobile.value && currentlyMobile) {
    // 从桌面端 ==> 切换到移动端
    // (userDesktopPrefersCollapsed 已经记录了切换前的桌面端偏好)
    isMobile.value = true;
    isSidebarCollapse.value = true; // 在移动端强制折叠
  } else if (currentlyMobile) {
    // 仍然是移动端，但窗口大小可能改变 (例如屏幕旋转)
    isMobile.value = true; // 确保 isMobile 状态正确
    isSidebarCollapse.value = true; // 在移动端始终保持/强制折叠
  } else {
    // 仍然是桌面端，但窗口大小可能改变
    isMobile.value = false; // 确保 isMobile 状态正确
    // 在桌面端，保持由 toggleSidebar 或初始偏好设定的 isSidebarCollapse 状态
    // isSidebarCollapse.value = userDesktopPrefersCollapsed.value; // 这一行确保如果状态意外不同步，则恢复
  }
};

// 组件挂载时
onMounted(() => {
  const initiallyMobile = window.innerWidth < MOBILE_BREAKPOINT;
  isMobile.value = initiallyMobile;

  if (initiallyMobile) {
    isSidebarCollapse.value = true; // 移动端初始折叠
    // userDesktopPrefersCollapsed 保持其默认值 false (桌面端默认展开)
  } else {
    // 桌面端初始状态根据 userDesktopPrefersCollapsed (默认为 false，即展开)
    isSidebarCollapse.value = userDesktopPrefersCollapsed.value;
  }
  window.addEventListener('resize', updateLayoutBasedOnScreenWidth);
});

// 组件卸载前
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateLayoutBasedOnScreenWidth);
});

// 用户点击汉堡包图标切换侧边栏
const toggleSidebar = () => {
  isSidebarCollapse.value = !isSidebarCollapse.value;
  if (!isMobile.value) {
    // 如果当前是桌面端，用户的这个操作就是TA的桌面端偏好
    userDesktopPrefersCollapsed.value = isSidebarCollapse.value;
  }
  // 如果是移动端，isSidebarCollapse.value 会变为 false (展开抽屉)
  // updateLayoutBasedOnScreenWidth 会在窗口resize时再次强制折叠 (如果仍在移动端)
  // 这意味着移动端的抽屉可以被打开，但如果此时resize窗口，它会再次根据逻辑判断是否折叠
};

// (移动端) 点击遮罩层关闭侧边栏
const handleClickOutside = () => {
  if (isMobile.value && !isSidebarCollapse.value) { // 如果是移动端且侧边栏是展开状态
    isSidebarCollapse.value = true; // 折叠它
  }
};

// 用于给 app-wrapper 动态添加 class
const classObj = computed(() => ({
  hideSidebar: isSidebarCollapse.value,
  openSidebar: !isSidebarCollapse.value,
  mobile: isMobile.value
}));

</script>

<style scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

/* 移动端遮罩层 */
.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999; /* 比 sidebar 低，比 main-container 高 */
}


.sidebar-container {
  transition: width 0.28s;
  width: var(--sidebar-width) !important;
  background-color: var(--sidebar-bg-color);
  height: 100%;
  position: fixed; /* 改为 fixed，更符合通常后台布局 */
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
}

/* 当 app-wrapper 有 hideSidebar 类时，侧边栏折叠 */
.app-wrapper.hideSidebar .sidebar-container {
  width: var(--sidebar-collapsed-width) !important;
}



.main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: var(--sidebar-width);
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
}

.app-wrapper.hideSidebar .main-container {
  margin-left: var(--sidebar-collapsed-width);
}


.fixed-header {
  position: sticky;
  top: 0;
  z-index: 990; /* 比 drawer-bg 高，比 sidebar 低 */
  width: 100%;
}
</style>