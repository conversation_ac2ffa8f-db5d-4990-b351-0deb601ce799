<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <component :is="Component" :key="route.path" />
        </transition>
    </router-view>
  </section>
</template>

<script setup>
// import { computed } from 'vue'
// import { useStore } from 'vuex' // 如果需要从 store 获取 cachedViews

// const store = useStore()
// const cachedViews = computed(() => store.state.tagsView?.cachedViews || []) // 假设有 tagsView 模块
</script>

<style scoped>
.app-main {
  /* Navbar height */
  min-height: calc(100vh - var(--navbar-height));
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: var(--app-main-bg-color);
  padding: var(--app-main-padding); /* 内边距 */
  flex-grow: 1; /* 占据剩余空间 */
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .3s;
}
.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>