import { createRouter, createWebHistory } from 'vue-router'
import store from '@/store' // 用于导航守卫获取 token
import Layout from '@/layout/index.vue' // 主布局组件
// ElMessage 是自动导入的，如果未配置自动导入，则需要手动引入:
// import { ElMessage } from 'element-plus'

// 1. 导入 NProgress 和它的CSS样式
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 2. (可选) 配置 NProgress，例如隐藏默认的加载小圆圈 (spinner)
NProgress.configure({ showSpinner: false })

// 公共路由，所有用户可见
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录' },
    hidden: true // 自定义属性，不在侧边栏显示
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    meta: { title: '404 - 页面未找到' },
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401.vue'),
    meta: { title: '401 - 无权限' },
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        // meta 用于侧边栏标题、图标、面包屑等
        meta: { title: '首页', icon: 'House' } // 'House' 是 Element Plus 图标名
      }
    ]
  },
  {
    path: '/example',
    component: Layout,
    redirect: '/example/table',
    name: 'Example',
    meta: { title: '功能示例', icon: 'Menu' }, // 父级菜单
    children: [
      {
        path: 'table',
        name: 'ExampleTable',
        component: () => import('@/views/example/table.vue'),
        meta: { title: '表格示例', icon: 'Grid' }
      },
      {
        path: 'form',
        name: 'ExampleForm',
        component: () => import('@/views/example/form.vue'),
        meta: { title: '表单示例', icon: 'Tickets' }
      },
      {
        path: 'theme-test',
        name: 'ThemeTest',
        component: () => import('@/views/example/theme-test.vue'),
        meta: { title: '主题测试', icon: 'Brush' }
      },
      {
        path: 'icon-test',
        name: 'IconTest',
        component: () => import('@/views/example/icon-test.vue'),
        meta: { title: '图标测试', icon: 'Star' }
      }
    ]
  },
  {
    path: '/attendance',
    component: Layout,
    redirect: '/attendance/list',
    name: 'Attendance',
    meta: { title: '考勤管理', icon: 'Calendar' },
    children: [
      {
        path: 'list',
        name: 'AttendanceList',
        component: () => import('@/views/attendance/list.vue'),
        meta: { title: '考勤列表', icon: 'Calendar' }
      }
    ]
  }
  // 更多路由...
]

// 异步/权限路由 (如果需要基于角色动态添加路由)
// export const asyncRoutes = [ ... ]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL), // History 模式
  scrollBehavior: () => ({ top: 0 }), // 切换路由时滚动到页面顶部
  routes: constantRoutes
})

// 全局前置守卫
const whiteList = ['/login', '/404', '/401'] // 路由白名单，不需要登录即可访问

router.beforeEach(async (to, from, next) => {
    // 3. 在每次路由切换开始时启动 NProgress
    NProgress.start();
    // 设置页面标题
    document.title = `${to.meta.title || '后台管理'} - ${import.meta.env.VITE_APP_TITLE || 'Admin'}`

    const hasToken = store.getters.token

    if (hasToken) {
        if (to.path === '/login') {
            // 如果已登录，访问登录页则重定向到首页
            next({ path: '/' })
        } else {
            // 检查用户是否已获取其用户信息 (例如角色信息)
            const hasUserInfo = store.getters.name // 用 name 作为判断依据，实际可根据需要调整
            if (hasUserInfo) {
                next()
            } else {
                try {
                    // 异步获取用户信息
                    await store.dispatch('user/getInfo')
                    // 如果你有动态路由（asyncRoutes），在这里处理：
                    // const accessRoutes = await store.dispatch('permission/generateRoutes', store.getters.roles)
                    // accessRoutes.forEach(route => router.addRoute(route))
                    // next({ ...to, replace: true }) //确保动态添加的路由生效
                    next() // 当前模板没有动态路由，直接next()
                } catch (error) {
                    // 获取用户信息失败，token可能失效，重置token并跳转到登录页
                    await store.dispatch('user/resetToken')
                    ElMessage.error(error.message || '获取用户信息失败，请重新登录')
                    next(`/login?redirect=${to.path}`) // 跳转到登录页，并携带原目标路径
                }
            }
        }
    } else {
        // 没有 token
        if (whiteList.includes(to.path)) {
            // 在免登录白名单，直接进入
            next()
        } else {
            // 其他没有访问权限的页面将被重定向到登录页面
            next(`/login?redirect=${to.path}`)
        }
    }
})

// 4. 在每次路由切换结束（无论成功或失败）后关闭 NProgress
router.afterEach(() => {
  NProgress.done();
});

export default router