<template>
  <div class="actual-sidebar-root-element"> 
    <div class="sidebar-logo-container" :class="{'collapse': isCollapse}">
      <router-link to="/" class="sidebar-logo-link">
        <img v-if="logoUrl" :src="logoUrl" class="sidebar-logo" alt="Logo">
        <h1 v-if="!isCollapse" class="sidebar-title">{{ sidebarTitle }}</h1>
      </router-link>
    </div>
    <el-scrollbar class="sidebar-menu-scrollbar" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.sidebarBgColor"
        :text-color="variables.sidebarTextColor"
        :active-text-color="variables.sidebarActiveTextColor"
        :unique-opened="true" 
        :collapse-transition="false"
        mode="vertical"
        router
      >
        <SidebarItem
          v-for="route in menuRoutes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
          :is-collapse="isCollapse" 
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
// ... 脚本部分保持不变 ...
import { computed, reactive, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useDark } from '@vueuse/core'
import SidebarItem from '@/components/SidebarItem.vue'
import logoUrl from '@/assets/logo.png';

const props = defineProps({
  isCollapse: {
    type: Boolean,
    required: true
  }
})

// 使用 VueUse 的 useDark
const isDark = useDark({
  storageKey: 'admin-theme-mode'
})

const getCssVariableValue = (variableName, defaultValue) => {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement).getPropertyValue(variableName).trim() || defaultValue;
  }
  return defaultValue;
};

// 响应式的侧边栏样式变量
const variables = reactive({
  sidebarBgColor: getCssVariableValue('--sidebar-bg-color', '#ffffff'),
  sidebarTextColor: getCssVariableValue('--sidebar-text-color', '#606266'),
  sidebarActiveTextColor: getCssVariableValue('--sidebar-active-text-color', '#409EFF'),
});

// 监听主题变化，更新侧边栏样式（使用 VueUse）
watch(isDark, (newIsDark) => {
  // 使用 nextTick 确保 DOM 更新完成后再更新样式
  nextTick(() => {
    variables.sidebarBgColor = getCssVariableValue('--sidebar-bg-color', newIsDark ? '#1f2937' : '#ffffff')
    variables.sidebarTextColor = getCssVariableValue('--sidebar-text-color', newIsDark ? '#9ca3af' : '#606266')
    variables.sidebarActiveTextColor = getCssVariableValue('--sidebar-active-text-color', '#409EFF')
  })
}, { immediate: true })

const sidebarTitle = import.meta.env.VITE_APP_TITLE || 'Admin';
const route = useRoute();
const router = useRouter();
const menuRoutes = computed(() => {
  return router.options.routes.filter(item => !item.hidden);
});
const activeMenu = computed(() => {
  const { meta, path } = route;
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>

<style scoped>
/* 新增：确保根元素继承高度，如果 .sidebar-container 类应用在它上面 */
.actual-sidebar-root-element {
  height: 100%;
  display: flex; /* 使内部元素可以更好地控制 */
  flex-direction: column;
  background-color: var(--sidebar-bg-color); /* 确保整个 sidebar 区域有背景色 */
  border-right: 1px solid var(--sidebar-border-color);
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing);
}

.sidebar-logo-container {
  position: relative; /* 改为 relative，因为它现在是 flex item */
  width: 100%;
  height: var(--navbar-height);
  line-height: var(--navbar-height);
  background: var(--sidebar-logo-bg-color);
  text-align: center;
  overflow: hidden;
  flex-shrink: 0; /* 防止 logo 区域被压缩 */
  border-bottom: 1px solid var(--sidebar-border-color);
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing);
}

/* ... (logo, title 的其他样式保持不变) ... */
.sidebar-logo-container.collapse .sidebar-logo {
  margin-right: 0px;
}
.sidebar-logo-link {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}
.sidebar-logo-container:not(.collapse) .sidebar-logo-link {
  justify-content: flex-start;
  padding-left: 18px;
}
.sidebar-logo-container.collapse .sidebar-logo-link {
  justify-content: center;
}
.sidebar-logo {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  margin-right: 12px;
}
.sidebar-title {
  display: inline-block;
  margin: 0;
  color: var(--sidebar-logo-text-color);
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
  vertical-align: middle;
  transition: color var(--theme-transition-duration) var(--theme-transition-timing);
}


.sidebar-menu-scrollbar {
  /* height: calc(100% - var(--navbar-height)); 原来是这样 */
  flex-grow: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 确保垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动 */
}

:deep(.el-menu) {
  border-right: none;
  height: 100%;
  width: 100% !important;
  background-color: var(--sidebar-bg-color) !important;
}

/* 菜单项样式 */
:deep(.el-menu-item) {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing) !important;
}

:deep(.el-menu-item:hover) {
  background-color: var(--sidebar-hover-bg-color) !important;
}

:deep(.el-menu-item.is-active) {
  background-color: var(--sidebar-active-bg-color) !important;
  color: var(--sidebar-active-text-color) !important;
}

/* 子菜单样式 */
:deep(.el-sub-menu__title) {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing) !important;
}

:deep(.el-sub-menu__title:hover) {
  background-color: var(--sidebar-hover-bg-color) !important;
}

:deep(.el-sub-menu .el-menu-item) {
  background-color: var(--sidebar-bg-color) !important;
}

:deep(.el-sub-menu .el-menu-item:hover) {
  background-color: var(--sidebar-hover-bg-color) !important;
}

:deep(.el-sub-menu .el-menu-item.is-active) {
  background-color: var(--sidebar-active-bg-color) !important;
}

/* 折叠状态下的样式 */
:deep(.el-menu--collapse .el-sub-menu__title span),
:deep(.el-menu--collapse .el-sub-menu__title .el-icon.el-sub-menu__icon-arrow) {
  display: none !important;
}
</style>