import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 引入 Element Plus 的CSS (如果 vite.config.js 中 ElementPlusResolver 没有配置 style:true 或者你想全局引入)
// 通常，unplugin-vue-components 和 unplugin-auto-import 会处理组件的按需引入及其样式
// 但为了确保万无一失，可以保留全局样式引入
import 'element-plus/dist/index.css'
// 引入 Element Plus 暗黑模式样式
import 'element-plus/theme-chalk/dark/css-vars.css'

// 全局 CSS 变量和基础样式
import './styles/variables.css'
import './styles/index.css'

const app = createApp(App)

app.use(store)
app.use(router)

// VueUse 会自动处理主题初始化，无需手动调用

// 如果没有使用 unplugin-auto-import 和 unplugin-vue-components 自动导入 Element Plus，
// 则需要手动注册：
// import ElementPlus from 'element-plus'
// app.use(ElementPlus)

// 全局注册 Element Plus 图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')