{"name": "my-admin-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "element-plus": "^2.9.11", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "unplugin-auto-import": "^19.3.0", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5"}}