<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Admin Template</title>
    <!-- 防闪烁脚本 - VueUse 兼容版本 -->
    <script>
      (function() {
        const THEME_KEY = 'admin-theme-mode';
        const getSystemTheme = () => {
          return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        };

        // 读取保存的主题偏好
        const stored = localStorage.getItem(THEME_KEY);
        const isDark = stored === null ? getSystemTheme() : stored === 'true';

        // 立即应用主题，防止闪烁
        if (isDark) {
          document.documentElement.classList.add('dark');
        }
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>