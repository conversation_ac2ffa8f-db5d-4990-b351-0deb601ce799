/* 已经在 main.js 引入了 variables.css，这里主要放其他全局样式 */

/* 优化的全局过渡效果 - 减少闪烁 */
*,
*::before,
*::after {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

/* 主题切换时确保过渡一致性 */
html.theme-transitioning {
  /* 防止页面滚动时的闪烁 */
  overflow-x: hidden;
}

html.theme-transitioning *,
html.theme-transitioning *::before,
html.theme-transitioning *::after {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing) !important,
    color var(--theme-transition-duration) var(--theme-transition-timing) !important,
    border-color var(--theme-transition-duration) var(--theme-transition-timing) !important,
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing) !important;
}

/* 轻量级主题切换效果 */
.light-switch-ripple {
  position: fixed;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
}

/* 排除可能导致闪烁的元素 */
img,
video,
iframe,
canvas,
svg,
.el-loading-mask,
.el-message,
.el-notification,
.el-tooltip,
.el-popper,
.el-overlay,
.el-drawer,
.el-dialog,
.el-backtop,
*[class*="transition"],
*[class*="animate"],
*[class*="fade"],
*[class*="slide"] {
  transition: none !important;
}

/* 特别处理可能导致闪烁的 Element Plus 组件 */
.el-scrollbar,
.el-scrollbar__wrap,
.el-scrollbar__view {
  transition: none !important;
}

/* 对于动画和变换，使用更短的过渡时间 */
.el-button,
.el-input,
.el-card,
.theme-switch-container {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing),
    transform 0.2s var(--theme-transition-timing);
}

/* 优化 Element Plus 组件的过渡效果 */
.el-menu,
.el-menu-item,
.el-submenu,
.el-table,
.el-table th,
.el-table td,
.el-pagination,
.el-breadcrumb,
.el-breadcrumb__item {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing);
}

body {
  margin: 0;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--navbar-text-color, #303133); /* 使用主题变量 */
  font-size: 14px;
  background-color: var(--app-main-bg-color); /* 页面整体背景色 */
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

/* 通用清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* Element Plus 组件的滚动条美化 (可选) */
.el-scrollbar__wrap {
  overflow-x: hidden !important; /* 防止横向滚动条 */
}

/* 确保应用包裹器占满 */
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}