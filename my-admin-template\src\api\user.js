import axios from 'axios' // 直接使用axios，因为需要调用外部接口
import request from '@/utils/request' // 引入封装好的 axios 实例，用于其他接口

export function loginApi(data) {
  return axios({
    url: 'http://119.91.207.203/api/Token/GetCookie?userID=' + data.userID + '&password=' + data.password,
    method: 'post',
    timeout: 10000
  })
}

export function getUserInfoApi() { // token 通常在请求拦截器中统一处理
  return request({
    url: '/user/info', // 获取用户信息的接口路径
    method: 'get'
  })
}

export function logoutApi() {
  return request({
    url: '/user/logout', // 登出接口路径
    method: 'post'
  })
}

// 获取近1个月考勤数据
export function getWechatAttendanceApi(userID) {
  return axios({
    url: `http://119.91.207.203/api/Attendance/GetWechatAttendance?userID=${userID}`,
    method: 'get',
    timeout: 10000
  })
}

// 获取历史考勤数据（1个月之前）
export function getGeneralAttendanceApi(data) {
  console.log('发送历史数据请求:', {
    url: `http://119.91.207.203/api/Attendance/GetGeneralAttendance?cookie=${data.cookie.substring(0, 20)}...`,
    method: 'post',
    data: {
      userID: data.userID,
      startDate: data.startDate,
      endDate: data.endDate
    }
  })

  return axios({
    url: `http://119.91.207.203/api/Attendance/GetGeneralAttendance?cookie=${data.cookie}`,
    method: 'post',
    data: {
      userID: data.userID,
      startDate: data.startDate,
      endDate: data.endDate
    },
    timeout: 30000, // 增加超时时间到30秒
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取工作日报数据
export function getWechatWorkdailyApi(data) {
  console.log('发送工作日报请求:', {
    url: 'http://119.91.207.203/api/WorkDaily/GetWechatWorkdaily',
    method: 'post',
    data: {
      userID: data.userID,
      startDate: data.startDate,
      endDate: data.endDate
    }
  })

  return axios({
    url: 'http://119.91.207.203/api/WorkDaily/GetWechatWorkdaily',
    method: 'post',
    data: {
      userID: data.userID,
      startDate: data.startDate,
      endDate: data.endDate
    },
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 你可以在这里添加更多用户相关的API函数
// export function updateUserProfileApi(data) {
//   return request({
//     url: '/user/profile',
//     method: 'put',
//     data
//   })
// }