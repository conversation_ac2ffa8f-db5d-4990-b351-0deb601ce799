<template>
    <div class="table-example-container">
        <el-card>
            <template #header>表格示例</template>
            <div class="table-responsive-wrapper">
                <el-table :data="tableData" style="width: 100%" border stripe>
                    <el-table-column prop="date" label="日期" width="180" />
                    <el-table-column prop="name" label="姓名" width="180" />
                    <el-table-column prop="address" label="地址" />
                    <el-table-column label="操作" width="150">
                        <template #default="scope">
                            <el-button size="small" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
                            <el-button size="small" type="danger"
                                @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <el-pagination style="margin-top: 20px; justify-content: flex-end;" background
                layout="total, sizes, prev, pager, next, jumper" :total="totalItems" :page-sizes="[10, 20, 50, 100]"
                v-model:current-page="currentPage" v-model:page-size="pageSize" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </el-card>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
// ElMessage 是自动导入的   

const tableData = ref([
  { date: '2023-01-01', name: '张三', address: '上海市普陀区金沙江路 1518 弄' },
  { date: '2023-01-02', name: '李四', address: '上海市普陀区金沙江路 1517 弄' },
  { date: '2023-01-03', name: '王五', address: '上海市普陀区金沙江路 1519 弄' },
  { date: '2023-01-04', name: '赵六', address: '上海市普陀区金沙江路 1516 弄' }
])
const totalItems = ref(tableData.value.length * 10); // 假设总共有这么多数据
const currentPage = ref(1);
const pageSize = ref(10);


const handleEdit = (index, row) => {
  ElMessage.info(`编辑了行: ${row.name}`)
  console.log(index, row)
}
const handleDelete = (index, row) => {
  ElMessage.warning(`删除了行: ${row.name}`)
  tableData.value.splice(index, 1) // 仅为演示，实际应调用API
}

const handleSizeChange = (val) => {
  console.log(`${val} items per page`);
  // 在这里重新获取数据
}
const handleCurrentChange = (val) => {
  console.log(`current page: ${val}`);
  // 在这里重新获取数据
}

// ---- 新增：用于判断屏幕大小的逻辑 ----
const screenWidth = ref(window.innerWidth);
// 定义一个小屏幕的断点，例如 Element Plus 的 'sm' 断点是 768px
const IS_SMALL_SCREEN_BREAKPOINT = 768;
const isSmallScreen = computed(() => screenWidth.value < IS_SMALL_SCREEN_BREAKPOINT);

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth);
  updateScreenWidth(); // 初始化执行一次
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateScreenWidth);
});
// ---- 屏幕大小判断逻辑结束 ----
</script>

<style scoped>
.table-example-container {
  padding: 20px;
}
.table-responsive-wrapper {
  width: 100%;
  overflow-x: auto; /* 关键：允许容器横向滚动 */
}
.custom-pagination {
  margin-top: 20px;
  display: flex; /* Element Plus 内部已经是 flex，这里是确保外部控制 */
  justify-content: flex-end; /* 默认右对齐 */
  flex-wrap: wrap; /* 允许分页组件内部的元素换行 */
  gap: 8px; /* 如果换行，元素之间有点间距 */
}

/* 当屏幕非常小，且分页组件换行后，可能希望它居中显示 */
@media (max-width: 480px) { /* 一个更小的断点示例 */
  .custom-pagination {
    justify-content: center;
  }
}
</style>