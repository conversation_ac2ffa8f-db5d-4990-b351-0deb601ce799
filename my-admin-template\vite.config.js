import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import path from 'path' // 确保导入 path 模块

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ['vue', 'vue-router', 'vuex'],
      // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
      resolvers: [
        ElementPlusResolver(),
        // 自动导入图标组件
        IconsResolver({
          prefix: 'Icon', // 访问图标组件时，可使用"Icon"前缀，例如 <IconEpEdit />
        }),
      ],
      dts: path.resolve(__dirname, 'src/auto-imports.d.ts'), // 指定自动导入类型声明文件路径
    }),
    Components({
      resolvers: [
        // 自动注册 Element Plus 组件
        ElementPlusResolver(),
        // 自动注册图标组件
        IconsResolver({
          enabledCollections: ['ep'], // 'ep' 代表 Element Plus icons
        }),
      ],
      dts: path.resolve(__dirname, 'src/components.d.ts'), // 指定自动导入组件类型声明文件路径
    }),
    Icons({
      autoInstall: true,
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src') // 设置 '@' 指向 'src' 目录
    }
  },
  server: {
    port: 8080, // 可选：指定开发服务器端口
    open: true, // 可选：自动打开浏览器
    proxy: {
      // 代理配置，用于开发环境解决跨域问题
      '/api': {
        target: 'http://localhost:3000', // 将请求代理到目标服务器 (请替换为你的后端API地址)
        changeOrigin: true, // 是否改变源地址
        // rewrite: (path) => path.replace(/^\/api/, '') // 可选：重写请求路径
      }
    }
  }
})