<template>
  <div class="dashboard-container">
    <el-row :gutter="20" class="stat-cards-row">
      <el-col :xs="24" :sm="12" :md="12" :lg="6" v-for="card in statCards" :key="card.title" class="stat-card-col">
        <el-card shadow="hover">
          <div class="stat-card-content">
            <div class="stat-icon-wrapper" :style="{ backgroundColor: card.iconBgColor }">
              <el-icon :size="32" class="stat-icon" :color="card.iconColor || 'white'">
                <component :is="card.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ card.title }}</div>
              <el-statistic :value="card.value" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="main-panels-row">
      <el-col :xs="24" :lg="16" class="panel-col">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势 (图表占位)</span>
            </div>
          </template>
          <div class="chart-placeholder">
            这里可以放置折线图、柱状图等图表组件
            <br>
            (例如使用 ECharts, Chart.js 等库)
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="8" class="panel-col">
        <el-card shadow="hover" class="info-card">
          <template #header>
            <div class="card-header">
              <span>待办事项/通知 (占位)</span>
            </div>
          </template>
          <ul class="info-list">
            <li><el-tag type="success" size="small" effect="plain">完成</el-tag> 用户注册模块开发</li>
            <li><el-tag type="warning" size="small" effect="plain">进行中</el-tag> 报表功能需求评审</li>
            <li><el-tag type="danger" size="small" effect="plain">高</el-tag> 修复生产环境BUG-1024</li>
            <li><el-tag size="small" effect="plain">计划</el-tag> 下季度产品规划会议</li>
            <li><el-tag type="info" size="small" effect="plain">通知</el-tag> 系统将于今晚2点进行维护</li>
          </ul>
        </el-card>
      </el-col>
    </el-row>

    </div>
</template>

<script setup>
import { ref } from 'vue';
// 假设这些图标已经通过 unplugin-icons 自动导入，或者您需要在此显式导入
// 例如: import { User, ChatDotRound, ShoppingCart, Money } from '@element-plus/icons-vue';

const statCards = ref([
  { title: '新增用户', value: 1024, icon: 'User', iconColor: '#FFFFFF', iconBgColor: '#40c9c6' },
  { title: '待处理消息', value: 812, icon: 'ChatDotRound', iconColor: '#FFFFFF', iconBgColor: '#36a3f7' },
  { title: '商品订单', value: 9280, icon: 'ShoppingCart', iconColor: '#FFFFFF', iconBgColor: '#f4516c' },
  { title: '今日收入 (元)', value: 13600.50, icon: 'Money', iconColor: '#FFFFFF', iconBgColor: '#34bfa3' }
]);

// 实际项目中，图表数据和列表数据通常是从API异步获取的
</script>

<style scoped>
.dashboard-container {
  padding: 20px; /* 或者使用 var(--app-main-padding) */
  /* background-color: var(--app-main-bg-color); /* AppMain自身通常有背景色 */
  min-height: calc(100vh - var(--navbar-height) - 40px); /* 减去navbar高度和自身padding */
}

.stat-cards-row .stat-card-col,
.main-panels-row .panel-col {
  margin-bottom: 20px;
}

.stat-card-content {
  display: flex;
  align-items: center;
}

.stat-icon-wrapper {
  padding: 12px;
  border-radius: 6px;
  margin-right: 15px;
  display: flex; /* 用于居中图标，如果图标本身不是block */
  align-items: center;
  justify-content: center;
}

.stat-icon {
  /* el-icon 已经有 display: inline-flex */
}

.stat-info {
  flex-grow: 1;
}

.stat-title {
  color: #8c8c8c;
  font-size: 14px;
  margin-bottom: 8px;
}

/* Element Plus el-statistic 的默认样式可能较大，可以按需调整 */
:deep(.el-statistic .el-statistic__head) {
  /* 如果使用自定义标题 .stat-title，可以隐藏 el-statistic 的默认头部 */
  /* display: none; */
  font-size: 14px !important; /* 或者调整它的大小 */
  color: #8c8c8c !important;
  margin-bottom: 8px !important;
}
:deep(.el-statistic .el-statistic__content) {
  font-size: 24px !important; /* 调整数值的字体大小 */
  font-weight: bold;
  color: #303133; /* 确保数值颜色 */
}

.chart-card, .info-card {
  min-height: 350px; /* 给卡片一个最小高度 */
  display: flex; /* 使卡片内容可以 flex 布局 */
  flex-direction: column;
}

.chart-card :deep(.el-card__body),
.info-card :deep(.el-card__body) {
  flex-grow: 1; /* 让卡片主体部分填充剩余空间 */
  display: flex; /* 方便内部元素布局 */
  flex-direction: column;
}


.chart-placeholder {
  flex-grow: 1; /* 占据卡片主体剩余空间 */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #a0a0a0;
  font-size: 1.2em;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  background-color: #f9fafc;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}
.info-list li {
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.info-list li:last-child {
  border-bottom: none;
}
.info-list li .el-tag {
  margin-right: 8px;
}

.card-header {
  font-weight: bold;
}
</style>