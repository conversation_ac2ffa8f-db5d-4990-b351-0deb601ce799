<template>
  <div class="form-example-container">
    <el-card>
      <template #header>表单示例</template>
      <el-form :model="form" label-width="120px" style="max-width: 600px;">
        <el-form-item label="活动名称">
          <el-input v-model="form.name" placeholder="请输入活动名称" />
        </el-form-item>
        <el-form-item label="活动区域">
          <el-select v-model="form.region" placeholder="请选择活动区域">
            <el-option label="区域一" value="shanghai" />
            <el-option label="区域二" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="活动时间">
          <el-col :span="11">
            <el-date-picker v-model="form.date1" type="date" placeholder="选择日期" style="width: 100%;" />
          </el-col>
          <el-col :span="2" class="text-center">
            <span class="text-gray-500">-</span>
          </el-col>
          <el-col :span="11">
            <el-time-picker v-model="form.date2" placeholder="选择时间" style="width: 100%;" />
          </el-col>
        </el-form-item>
        <el-form-item label="即时配送">
          <el-switch v-model="form.delivery" />
        </el-form-item>
        <el-form-item label="活动性质">
          <el-checkbox-group v-model="form.type">
            <el-checkbox value="美食/餐厅线上活动" name="type">美食/餐厅线上活动</el-checkbox>
            <el-checkbox value="地推活动" name="type">地推活动</el-checkbox>
            <el-checkbox value="线下主题活动" name="type">线下主题活动</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="特殊资源">
          <el-radio-group v-model="form.resource">
            <el-radio value="线上品牌商赞助">线上品牌商赞助</el-radio>
            <el-radio value="线下场地免费">线下场地免费</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="活动形式">
          <el-input v-model="form.desc" type="textarea" placeholder="请输入活动形式" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">创建</el-button>
          <el-button @click="onCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
// ElMessage 是自动导入的

const form = reactive({
  name: '',
  region: '',
  date1: '',
  date2: '',
  delivery: false,
  type: [],
  resource: '',
  desc: ''
})

const onSubmit = () => {
  ElMessage.success('表单已提交！')
  console.log('submit!', form)
  // 在这里可以添加API调用逻辑
}

const onCancel = () => {
  ElMessage.info('表单已取消')
  // 可以添加重置表单的逻辑
}
</script>

<style scoped>
.form-example-container {
  padding: 20px;
}
.text-center {
  text-align: center;
}
</style>