import axios from 'axios'
import store from '@/store'
import { getToken } from '@/utils/auth'
// ElMessage, ElMessageBox 是自动导入的，如果未配置自动导入，则需要手动引入:
// import { ElMessage, ElMessageBox } from 'element-plus'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API, // API 的 base_url, 从 .env 文件读取
  timeout: 10000 // 请求超时时间 (毫秒)
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    if (store.getters.token) {
      // 让每个请求携带自定义 token 请根据实际情况自行修改
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('Request Error:', error) // for debug
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  /**
   * 如果你想获取诸如 headers 或 status 之类的http信息
   * 请返回 response => response
  */
  response => {
    const res = response.data // 我们通常只关心服务端返回的 data 部分

    // 假设与后端约定：code 非 200 (或 20000) 都视为错误
    // 这个 code 判断逻辑需要根据你的后端接口规范来调整
    if (res.code !== 200 && res.code !== 20000) {
      ElMessage({
        message: res.message || '请求错误',
        type: 'error',
        duration: 5 * 1000
      })

      // 50008: 非法token; 50012: 其他客户端登录; 50014: Token 过期;
      // 这些错误码也需要根据你的后端接口规范来定义
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        // 重新登录
        ElMessageBox.confirm('您的登录状态已过期或在其他设备登录，请重新登录。', '确认登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload() // 为了重新实例化vue-router对象，避免bug
          })
        }).catch(() => {
          // 用户点击取消，可以不做处理或给出提示
        });
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      // code 为 200 (或 20000), 表示请求成功
      return res // 直接返回 res (通常是 res.data)
    }
  },
  error => {
    console.error('Response Error:', error) // for debug
    ElMessage({
      message: error.message || '网络请求异常，请稍后再试',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service