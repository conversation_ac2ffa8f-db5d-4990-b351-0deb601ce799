import { createStore } from 'vuex'
import user from './modules/user'
import theme from './modules/theme'
// import permission from './modules/permission' // 如果将来添加权限模块

// 全局 getters，方便访问模块内的状态
const getters = {
  token: state => state.user.token,
  name: state => state.user.name,
  avatar: state => state.user.avatar,
  roles: state => state.user.roles,
  userID: state => state.user.userID, // 添加 userID getter
  // 主题相关
  isDark: state => state.theme.isDark,
  currentTheme: state => state.theme.currentTheme,
  // permission_routes: state => state.permission.routes, // 如果有权限路由模块
}

const store = createStore({
  modules: {
    user,
    theme,
    // permission
  },
  getters
})

export default store