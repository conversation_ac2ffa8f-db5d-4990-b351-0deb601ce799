<template>
  <div class="navbar">
    <div class="hamburger-container" @click="toggleSideBar">
      <el-icon :size="20">
        <Fold v-if="!isSidebarCollapseProp" />
        <Expand v-if="isSidebarCollapseProp" />
      </el-icon>
    </div>

    <el-breadcrumb class="app-breadcrumb" separator="/"> <transition-group name="breadcrumb">
        <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
          <span v-if="index === breadcrumbs.length - 1" class="no-redirect">
            {{ item.meta.title }}
          </span>
          <a v-else @click.prevent="handleLink(item)">
            {{ item.meta.title }}
          </a>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>

    <div class="right-menu">
      <el-tooltip :content="isDark ? '切换到亮色模式' : '切换到暗黑模式'" placement="bottom">
        <div
          class="theme-switch-container right-menu-item"
          :class="{ 'theme-changing': isThemeChanging }"
          @click="toggleTheme($event)"
        >
          <el-icon :size="20">
            <Moon v-if="isDark" />
            <Sunny v-else />
          </el-icon>
        </div>
      </el-tooltip>
      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatarUrl" class="user-avatar" alt="avatar">
          <span class="user-name">{{ userName }}</span>
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/">
              <el-dropdown-item>首页</el-dropdown-item>
            </router-link>
            <router-link to="/example/table">
              <el-dropdown-item>示例表格</el-dropdown-item>
            </router-link>
            <el-dropdown-item divided @click="logout">
              <span style="display:block;">退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'
import { useDark, useToggle } from '@vueuse/core'
import { Fold, Expand, Moon, Sunny } from '@element-plus/icons-vue' // 新增：显式导入图标

// 图标 Fold, Expand, ArrowDown 会通过 unplugin-icons 自动注册为 <Fold />, <Expand />, <ArrowDown />

const props = defineProps({
  // 从 Layout.vue 接收侧边栏的折叠状态
  isSidebarCollapseProp: { // 重命名以区分内部变量，避免混淆
    type: Boolean,
    required: true
  }
})

const store = useStore()
const router = useRouter()
const route = useRoute(); // 获取当前路由信息

const userName = computed(() => store.getters.name || '用户')
const avatarUrl = computed(() => store.getters.avatar || 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png')

// 主题相关 - 使用 VueUse 的 useDark
const isDark = useDark({
  storageKey: 'admin-theme-mode',
  onChanged(dark) {
    // 添加过渡效果
    const html = document.documentElement
    html.classList.add('theme-transitioning')

    // 过渡完成后移除过渡类
    setTimeout(() => {
      html.classList.remove('theme-transitioning')
    }, 300)
  }
})

const toggleDark = useToggle(isDark)

// 添加点击动画状态
const isThemeChanging = ref(false)

const toggleTheme = (event) => {
  if (isThemeChanging.value) return // 防止重复点击

  isThemeChanging.value = true

  // 创建真正的涟漪扩散效果
  createRippleTransition(event)

  // 等待动画完成后重置状态
  setTimeout(() => {
    isThemeChanging.value = false
  }, 600)
}

// 创建涟漪扩散效果（无白屏版本）
const createRippleTransition = (event) => {
  // 获取点击位置
  const rect = event.currentTarget.getBoundingClientRect()
  const x = rect.left + rect.width / 2
  const y = rect.top + rect.height / 2

  // 计算需要覆盖整个屏幕的半径
  const maxRadius = Math.sqrt(
    Math.pow(Math.max(x, window.innerWidth - x), 2) +
    Math.pow(Math.max(y, window.innerHeight - y), 2)
  )

  // 先切换主题
  toggleDark()

  // 创建一个临时的页面快照作为旧主题的覆盖层
  const overlay = document.createElement('div')
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: ${!isDark.value ? '#f0f2f5' : '#111827'};
    pointer-events: none;
    z-index: 9998;
    clip-path: circle(${maxRadius}px at ${x}px ${y}px);
    transition: clip-path 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  `

  document.body.appendChild(overlay)

  // 开始收缩动画，露出下面的新主题
  requestAnimationFrame(() => {
    overlay.style.clipPath = `circle(0px at ${x}px ${y}px)`
  })

  // 动画完成后移除覆盖层
  setTimeout(() => {
    if (overlay.parentNode) {
      overlay.parentNode.removeChild(overlay)
    }
  }, 600)
}

const emit = defineEmits(['toggle-sidebar'])
const toggleSideBar = () => {
  emit('toggle-sidebar') // 通知父组件 Layout.vue 切换状态
}

// --- 面包屑逻辑 ---
const breadcrumbs = computed(() => {
  // route.matched 包含当前路由的所有嵌套路径片段的路由记录数组
  // 我们只关心那些在 meta 中定义了 title 的路由记录
  // 并且可以额外添加一个 meta.breadcrumb !== false 来允许某些路由不显示在面包屑中
  const matched = route.matched.filter(
    item => item.meta && item.meta.title && item.meta.breadcrumb !== false
  );
  
  // (可选) 特殊处理首页：如果当前不是首页，且匹配到的路径中不包含首页，可以考虑手动添加。
  // 但更通用的做法是确保路由结构中，根路径('/')或'/dashboard'本身就有 '首页' 的 meta.title。
  // 例如，如果我们的根路由 '/' 定义了 meta: { title: '首页' }，它自然会成为第一个。
  // 如果当前在 '/dashboard'，且 '/' 和 '/dashboard' 的 title 都是 '首页'，则面包屑是 '首页 / 首页'，
  // 这时，可以给 '/dashboard' 路由设置 meta: { breadcrumb: false } 或者一个不同的 title。
  //
  // 当前路由结构：
  // { path: '/', meta: { title: '首页' }, children: [ { path: 'dashboard', meta: { title: '控制台概览' }} ]}
  // 访问 /dashboard 时, matched 会是 [RouteRecordForPathSlash, RouteRecordForPathDashboard]
  // 它们都有 title，所以面包屑会是 "首页 / 控制台概览"
  //
  // 访问 /example/table 时, matched 会是 [RouteRecordForPathSlash, RouteRecordForExample, RouteRecordForExampleTable]
  // 面包屑会是 "首页 / 功能示例 / 表格示例"
  // 这个行为通常是期望的。

  return matched;
});

const handleLink = (item) => {
  // item 是一个路由记录对象 (RouteRecordMatched)
  // item.path 通常是完整的路径
  // item.redirect 也可以用来判断，但通常直接用 path
  if (item.path && item.path !== route.path) { // 防止重复导航到当前页
      router.push(item.path);
  }
};
// --- 面包屑逻辑结束 ---

const logout = async () => {
  await store.dispatch('user/logout')
  router.push(`/login?redirect=${router.currentRoute.value.fullPath}`)
}
</script>

<style scoped>
.navbar {
  height: var(--navbar-height);
  overflow: hidden;
  position: relative;
  background: var(--navbar-bg-color);
  box-shadow: var(--navbar-box-shadow);
  display: flex;
  align-items: center;
  padding: 0 15px;
}

.hamburger-container {
  line-height: var(--navbar-height);
  height: 100%;
  cursor: pointer;
  transition: background .3s;
  -webkit-tap-highlight-color:transparent;
  display: flex;
  align-items: center;
  padding-right: 15px;
}
.hamburger-container .el-icon {
  color: #515151;
}
.hamburger-container:hover {
  background: rgba(0, 0, 0, .025)
}

/* 面包屑容器样式 */
.app-breadcrumb.el-breadcrumb {
  display: inline-block; /* 或者 flex item，取决于和汉堡包图标的排列 */
  font-size: 14px;
  line-height: var(--navbar-height); /* 与导航栏高度对齐 */
  margin-left: 8px; /* 与汉堡包图标的间距 */
  flex-grow: 1; /* 让面包屑占据剩余空间 */
  min-width: 0; /* 允许收缩 */
}

/* 面包屑最后一项（当前页）的样式 */
.app-breadcrumb .no-redirect {
  color: #97a8be; /* 通常用灰色表示不可点击 */
  cursor: text;
}

/* 面包屑链接颜色 */
.app-breadcrumb a {
  color: #97a8be; /* 默认文字颜色 */
  font-weight: normal; /* 确保不是粗体 */
}
.app-breadcrumb a:hover {
  color: var(--primary-color); /* 主题色 */
}


.right-menu {
  display: flex;
  align-items: center;
}

.right-menu-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.theme-switch-container {
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--navbar-text-color);
  position: relative;
  overflow: hidden;
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    transform 0.2s var(--theme-transition-timing),
    box-shadow 0.2s var(--theme-transition-timing);
}

.theme-switch-container:hover {
  background-color: var(--theme-switch-hover-bg);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-switch-container:active {
  transform: scale(0.95);
}

/* 图标切换动画 */
.theme-switch-container .el-icon {
  transition:
    transform var(--theme-transition-duration) var(--theme-transition-timing),
    opacity 0.2s var(--theme-transition-timing);
}

.theme-switch-container:hover .el-icon {
  transform: rotate(15deg) scale(1.1);
}

/* 添加一个微妙的发光效果 */
.theme-switch-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, transparent 60%, var(--primary-color) 100%);
  opacity: 0;
  transition: opacity 0.3s var(--theme-transition-timing);
  pointer-events: none;
  border-radius: inherit;
}

.theme-switch-container:hover::before {
  opacity: 0.05;
}

/* 主题切换中的动画状态 */
.theme-switch-container.theme-changing {
  pointer-events: none; /* 防止重复点击 */
  transform: scale(0.9);
}

.theme-switch-container.theme-changing .el-icon {
  animation: theme-switch-spin 0.5s var(--theme-transition-timing);
}

@keyframes theme-switch-spin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}
.avatar-container {
  cursor: pointer;
  margin-left: 15px;
}
.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover; /* 新增：确保图片按比例填充并裁剪，不变形 */
  /* background-color: #f0f0f0; */ /* 可选：如果图片加载失败或有透明区域，给一个占位背景色 */
}

/* 面包屑过渡动画 (可选) */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
/* 确保离开的元素在动画期间不影响布局 */
.breadcrumb-leave-active {
  position: absolute;
}
.avatar-wrapper {
  display: flex;
  align-items: center; /* 这是实现垂直居中的关键 */
}

</style>