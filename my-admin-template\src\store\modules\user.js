// 引入真实的API调用
import { loginApi, getUserInfoApi, logoutApi } from '@/api/user'
import { getToken, setToken, removeToken, getUserID, setUserID, removeUserID } from '@/utils/auth' // Token 和 UserID 工具类
// import router from '@/router' // 如果需要在action中重置路由
// ElMessage 是自动导入的，如果未配置自动导入，则需要手动引入:
// import { ElMessage } from 'element-plus'


// --- Mock API Calls (模拟API，用于无后端演示) ---
const MOCK_DELAY = 300; // 模拟网络延迟

const mockLoginApi = (userInfo) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (userInfo.username === 'admin' && userInfo.password === '123456') {
        resolve({ code: 200, data: { token: 'mock-admin-token' } });
      } else if (userInfo.username === 'editor' && userInfo.password === '123456') {
        resolve({ code: 200, data: { token: 'mock-editor-token' } });
      } else {
        reject(new Error('用户名或密码错误 (mock)'));
      }
    }, MOCK_DELAY);
  });
};

// mockGetUserInfoApi 已移除，因为现在使用真实的登录token

const mockLogoutApi = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({ code: 200, message: '登出成功 (mock)' });
    }, MOCK_DELAY);
  });
};
// --- End of Mock API Calls ---


const state = {
  token: getToken(), // 从 localStorage 初始化 token
  name: '',
  avatar: '',
  roles: [], // 用户角色
  userID: getUserID() || '' // 从 localStorage 初始化 userID
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_USERID: (state, userID) => {
    state.userID = userID
  }
}

const getters = {
  userID: state => state.userID,
  token: state => state.token
}

const actions = {
  // 用户登录
  login({ commit }, userInfo) {
    const { userID, password } = userInfo
    return new Promise((resolve, reject) => {
      loginApi({ userID: userID.trim(), password: password })
        .then(response => {
          // 检查响应数据是否以JESSIONID开头
          const responseData = response.data
          if (typeof responseData === 'string' && responseData.startsWith('JSESSIONID')) {
            // 登录成功，将JESSIONID作为token存储，并保存userID
            commit('SET_TOKEN', responseData)
            commit('SET_USERID', userID.trim())
            setToken(responseData)
            setUserID(userID.trim()) // 将userID保存到localStorage
            resolve()
          } else {
            // 登录失败
            reject(new Error('用户ID或密码错误'))
          }
        }).catch(error => {
          console.error('登录请求失败:', error)
          reject(new Error('登录失败，请检查网络连接'))
        })
    })
  },

  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      // 如果有JSESSIONID token，直接设置用户信息
      if (state.token && state.token.startsWith('JSESSIONID')) {
        const userData = {
          name: state.userID || '用户', // 使用登录时的userID作为用户名
          avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
          roles: ['user']
        }
        commit('SET_NAME', userData.name)
        commit('SET_AVATAR', userData.avatar)
        commit('SET_ROLES', userData.roles)
        resolve(userData)
      } else {
        reject(new Error('无效的登录状态，请重新登录'))
      }
    })
  },

  // 用户登出
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      // 实际API: logoutApi(state.token)
      mockLogoutApi()
        .then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_NAME', '')
          commit('SET_AVATAR', '')
          commit('SET_USERID', '')
          removeToken() // 从 localStorage 移除 token
          removeUserID() // 从 localStorage 移除 userID
          // dispatch('tagsView/delAllViews', null, { root: true }) // 如果有标签页视图模块，清空
          // 重置路由 (如果需要动态添加路由，登出时需要重置)
          // resetRouter() // 这个函数需要额外在 router/index.js 中实现
          resolve()
        }).catch(error => {
          reject(error)
        })
    })
  },

  // 移除 token (例如 token 失效时前端主动清除)
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      commit('SET_NAME', '')
      commit('SET_AVATAR', '')
      commit('SET_USERID', '')
      removeToken()
      removeUserID() // 从 localStorage 移除 userID
      resolve()
    })
  }
}

export default {
  namespaced: true, // 开启命名空间，使得模块可以被更好地组织
  state,
  getters,
  mutations,
  actions
}