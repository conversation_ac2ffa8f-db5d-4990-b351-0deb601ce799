<template>
  <div class="login-container">
    <el-card class="login-card">
      <template #header>
        <div class="card-header">
          <span>用户登录</span>
        </div>
      </template>
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" @submit.prevent="handleLogin">
        <el-form-item prop="userID">
          <el-input
            v-model="loginForm.userID"
            placeholder="请输入用户ID"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="loading" style="width: 100%;">
            登 录
          </el-button>
        </el-form-item>
      </el-form>
      <div class="tips">
        <p>请输入您的用户ID和密码进行登录</p>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { User, Lock } from '@element-plus/icons-vue' // 手动引入图标，或确保已通过 unplugin-icons 自动注册
// ElMessage 是自动导入的

const store = useStore()
const router = useRouter()
const route = useRoute()

const loginFormRef = ref(null)
const loginForm = reactive({
  userID: '', // 用户ID
  password: ''
})

const loginRules = reactive({
  userID: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
})

const loading = ref(false)

const handleLogin = async () => {
  if (!loginFormRef.value) return
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await store.dispatch('user/login', loginForm)
        // 登录成功后，尝试获取用户信息
        await store.dispatch('user/getInfo');

        const redirect = route.query.redirect || '/'
        router.push(redirect)
        ElMessage.success('登录成功！')
      } catch (error) {
        ElMessage.error(error.message || '登录失败，请检查凭据')
      } finally {
        loading.value = false
      }
    } else {
      console.log('表单校验失败!')
      return false
    }
  })
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f0f2f5; /* 与 app-main 背景色一致或自定义 */
}
.login-card {
  width: 400px;
}
.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}
.tips {
  margin-top: 20px;
  font-size: 12px;
  color: #909399;
  text-align: center;
}
.tips p {
  margin: 5px 0;
}
</style>