<template>
  <div class="error-page">
    <el-result icon="warning" title="401 - 无权限访问" sub-title="抱歉，您没有权限访问此页面。">
      <template #extra>
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </template>
    </el-result>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
const goHome = () => {
  router.push('/')
}
const goBack = () => {
  router.go(-1)
}
</script>

<style scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - var(--navbar-height) - var(--app-main-padding) * 2);
  min-height: 400px;
}
</style>