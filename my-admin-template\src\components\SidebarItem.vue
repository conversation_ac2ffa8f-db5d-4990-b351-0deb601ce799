<template>
  <div v-if="!item.hidden">
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{'submenu-title-noDropdown':!isNest}">
        <el-icon v-if="onlyOneChild.meta && onlyOneChild.meta.icon">
          <component :is="onlyOneChild.meta.icon" />
        </el-icon>
        <template #title>{{ onlyOneChild.meta.title }}</template>
      </el-menu-item>
    </template>

    <el-sub-menu v-else :index="resolvePath(item.path)" popper-append-to-body>
      <template #title>
        <el-icon v-if="item.meta && item.meta.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta.title }}</span>
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import path from 'path-browserify' // 用于解析路径，确保安装: npm install path-browserify
// Element Plus 图标已在 main.js 中全局注册

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isNest: { // 是否为嵌套菜单（子菜单）
    type: Boolean,
    default: false
  },
  basePath: { // 父级路径
    type: String,
    default: ''
  },
  isCollapse: { // 侧边栏是否折叠，用于某些特殊处理 (此模板中暂时未使用)
    type: Boolean,
    default: false
  }
})

const onlyOneChild = ref(null)

// 图标已全局注册，可以直接使用字符串名称

function hasOneShowingChild(children = [], parent) {
  const showingChildren = children.filter(child => {
    if (child.hidden) {
      return false
    } else {
      // 临时设置（如果只有一个显示的子项，则使用）
      onlyOneChild.value = child
      return true
    }
  })

  // 当只有一个子路由时，默认显示子路由
  if (showingChildren.length === 1) {
    return true
  }

  // 如果没有子路由可显示，则显示父路由本身
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
}

function resolvePath(routePath) {
  // 检查是否是外部链接
  if (/^(https?:|mailto:|tel:)/.test(routePath)) {
    return routePath
  }
  // 使用 path.resolve 来正确拼接路径
  return path.resolve(props.basePath, routePath)
}
</script>

