// 主题模块 - 使用 VueUse 的 useDark 实现
import { useDark, useToggle } from '@vueuse/core'

// 使用 VueUse 的 useDark，它会自动处理 localStorage 和系统主题
const isDark = useDark({
  selector: 'html',
  attribute: 'class',
  valueDark: 'dark',
  valueLight: '',
  storageKey: 'admin-theme-mode',
  onChanged(dark) {
    // 添加过渡效果
    const html = document.documentElement
    html.classList.add('theme-transitioning')

    // 过渡完成后移除过渡类
    setTimeout(() => {
      html.classList.remove('theme-transitioning')
    }, 300)
  }
})

const toggleDark = useToggle(isDark)

const state = {
  // VueUse 会自动管理状态，这里只是为了兼容现有的 getter
}

const mutations = {
  // 保留为了兼容性，但实际由 VueUse 管理
}

const actions = {
  // 切换主题
  toggleTheme() {
    toggleDark()
  },

  // 设置特定主题
  setTheme(_, theme) {
    if (theme === 'dark') {
      isDark.value = true
    } else if (theme === 'light') {
      isDark.value = false
    }
  },

  // 初始化主题（VueUse 会自动初始化）
  initTheme() {
    // VueUse 会自动处理初始化，包括从 localStorage 读取和监听系统主题
    // 这里只需要确保初始状态正确
    const html = document.documentElement
    if (isDark.value) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }
}

const getters = {
  isDark: () => isDark.value,
  isLight: () => !isDark.value,
  currentTheme: () => isDark.value ? 'dark' : 'light',
  systemTheme: () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return 'light'
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
