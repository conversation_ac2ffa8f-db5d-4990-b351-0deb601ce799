/* 亮色模式（默认） */
:root {
  --primary-color: #409EFF; /* Element Plus 默认主色 */

  --sidebar-width: 210px;
  --sidebar-collapsed-width: 64px;
  /* 亮色模式侧边栏 - 使用更亮的配色 */
  --sidebar-bg-color: #ffffff;
  --sidebar-text-color: #606266;
  --sidebar-active-text-color: var(--primary-color);
  --sidebar-hover-bg-color: #f5f7fa;
  --sidebar-active-bg-color: #ecf5ff;
  --sidebar-logo-text-color: #303133;
  --sidebar-logo-bg-color: #f8f9fa;
  --sidebar-border-color: #e4e7ed;

  --navbar-height: 50px;
  --navbar-bg-color: #ffffff;
  --navbar-text-color: #303133;
  --navbar-box-shadow: 0 1px 4px rgba(0,21,41,.08);

  --app-main-bg-color: #f0f2f5; /* 内容区域背景色 */
  --app-main-padding: 20px;

  /* 优化的过渡动画 - 减少闪烁 */
  --theme-transition-duration: 0.3s;
  --theme-transition-timing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --el-transition-duration: 0.28s;

  /* 主题切换按钮样式 */
  --theme-switch-hover-bg: rgba(0, 0, 0, 0.1);
}

/* 暗黑模式 */
html.dark {
  /* 暗黑模式侧边栏 - 使用深色配色 */
  --sidebar-bg-color: #1f2937;
  --sidebar-text-color: #9ca3af;
  --sidebar-active-text-color: var(--primary-color);
  --sidebar-hover-bg-color: #374151;
  --sidebar-active-bg-color: #1e3a8a;
  --sidebar-logo-text-color: #f9fafb;
  --sidebar-logo-bg-color: #111827;
  --sidebar-border-color: #374151;

  --navbar-bg-color: #1f2937;
  --navbar-text-color: #e5e7eb;
  --navbar-box-shadow: 0 1px 4px rgba(0,0,0,.3);

  --app-main-bg-color: #111827;

  /* 主题切换按钮样式 */
  --theme-switch-hover-bg: rgba(255, 255, 255, 0.1);
}